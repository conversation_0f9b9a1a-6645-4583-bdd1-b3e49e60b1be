/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 通用工具类 */
.hidden {
    display: none !important;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    /* 背景图片，如果图片不存在则使用渐变背景 */
    background:
        url('assets/background.png') center center/cover no-repeat,
        linear-gradient(135deg, #87CEEB 0%, #98FB98 30%, #90EE90 70%, #228B22 100%);
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100); /* 小程序适配 */
    overflow: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* 禁用用户选择和缩放 */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    margin: 0;
    padding: 0;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    height: calc(var(--vh, 1vh) * 100); /* 小程序适配 */
    max-width: 100%;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

/* 上半部分：背景和机器人 */
.top-section {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 40vh;
    padding: 20px;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
}

.robot-container {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: -30px; /* 向下移动，使机器人下边缘与透明框上边缘对齐 */
}

.robot-avatar {
    width: 400px;
    height: 400px;
    max-width: 70vw;
    max-height: 70vw;
    /* 如果图片加载失败的回退样式 */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 120px;
    color: #4C79FF;
    /* 保持图片比例 */
    object-fit: contain;
    /* 添加一些视觉效果 */
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.1));
    transition: transform 0.3s ease;
}

.robot-avatar:hover {
    transform: scale(1.05);
}

/* 当图片加载失败时显示机器人emoji */
.robot-avatar::before {
    content: '🤖';
    display: none;
}

.robot-avatar[src="tongtong.gif"]::before,
.robot-avatar:not([src])::before {
    display: block;
}

.speech-bubble {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 16px 20px;
    max-width: 280px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    margin-bottom: 10px;
}

.speech-bubble::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid rgba(255, 255, 255, 0.95);
}

.speech-bubble p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
}

.speech-bubble p:first-child {
    margin-bottom: 8px;
    font-weight: 500;
}

/* 下半部分：对话区域 */
.bottom-section {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    height: calc(60vh - 80px);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(20px);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.15);
    margin: 0 12px 80px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}





/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4C79FF 0%, #6366F1 100%);
    color: white;
    padding: 20px 16px 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 10;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    opacity: 0.9;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #10B981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        will-change: opacity;
    }
    50% {
        opacity: 0.5;
        will-change: opacity;
    }
}

/* 聊天容器 */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: transparent;
    scroll-behavior: smooth;
    margin-bottom: 20px;
}

.chat-messages::-webkit-scrollbar {
    width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 2px;
}

/* 消息样式 */
.message {
    display: flex;
    align-items: flex-start;
    margin-bottom: 18px;
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
        will-change: opacity, transform;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        will-change: opacity, transform;
    }
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
    margin: 0 4px;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 14px;
}

.message-content {
    max-width: 70%;
    display: flex;
    flex-direction: column;
}

.message-text {
    background: rgba(255, 255, 255, 0.9);
    padding: 14px 18px;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
    line-height: 1.6;
    word-wrap: break-word;
    position: relative;
    -webkit-backdrop-filter: blur(15px);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    font-size: 15px;
    color: #2C3E50;
}

.user-message .message-text {
    background: linear-gradient(135deg, #2E7D32, #388E3C);
    color: white;
    border: 1px solid rgba(46, 125, 50, 0.4);
    box-shadow: 0 4px 25px rgba(46, 125, 50, 0.25);
}

.bot-message .message-text::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 12px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid rgba(255, 255, 255, 0.8);
}

.user-message .message-text::before {
    content: '';
    position: absolute;
    right: -8px;
    top: 12px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid rgba(76, 121, 255, 0.8);
}

.message-time {
    font-size: 11px;
    color: #64748b;
    margin-top: 4px;
    text-align: left;
}

.user-message .message-time {
    text-align: right;
}

/* 输入区域 */
.input-container {
    position: fixed;
    bottom: 16px;
    left: 12px;
    right: 12px;
    z-index: 100;
}

/* 输入行布局 */
.input-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 新对话内联按钮样式 */
.new-chat-inline-button {
    width: 56px; /* 减小到更合适的尺寸 */
    height: 56px; /* 减小到更合适的尺寸 */
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.15);
    color: #666;
    flex-shrink: 0; /* 防止按钮被压缩 */
    box-sizing: border-box; /* 确保边框包含在尺寸内 */
}

.new-chat-inline-button:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.2);
    color: #333;
    border-color: rgba(46, 125, 50, 0.3);
}

.new-chat-inline-button:active {
    transform: translateY(0);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.input-wrapper {
    display: flex;
    align-items: center; /* 居中对齐 */
    gap: 8px;
    background: rgba(255, 255, 255, 0.85);
    border-radius: 10px;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.15);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    flex: 1; /* 让输入框占据剩余空间 */
    height: 56px; /* 固定高度与新对话按钮一致 */
    box-sizing: border-box; /* 确保边框和padding包含在尺寸内 */
}

.input-wrapper:focus-within {
    border-color: #2E7D32;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 6px 30px rgba(46, 125, 50, 0.2);
    transform: translateY(-2px);
}



.message-input {
    flex: 1;
    border: none;
    background: transparent;
    resize: none;
    outline: none;
    font-size: 16px;
    line-height: 20px; /* 合适的行高 */
    height: 32px; /* 固定高度，与容器内部空间匹配 */
    font-family: inherit;
    color: #2C3E50;
    padding: 6px 0; /* 上下padding实现垂直居中 (32-20)/2 = 6 */
    vertical-align: middle;
    overflow-y: auto; /* 内容超出时显示滚动条 */
    word-wrap: break-word; /* 长单词自动换行 */
    white-space: pre-wrap; /* 保持换行和空格 */
    box-sizing: border-box; /* 确保padding包含在高度内 */
}

.message-input::placeholder {
    color: #94a3b8;
    line-height: 24px;
}

.send-button {
    width: 40px;
    height: 40px;
    border: none;
    background: linear-gradient(135deg, #2E7D32, #388E3C);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    box-shadow: 0 2px 10px rgba(46, 125, 50, 0.2);
}

.send-button:hover:not(:disabled) {
    transform: scale(1.08);
    box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
}

.send-button:disabled {
    background: #cbd5e1;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.input-footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 4px;
}

.char-count {
    font-size: 11px;
    color: #94a3b8;
}

/* 加载指示器 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #e2e8f0;
    border-top: 3px solid #4C79FF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast提示 */
.toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #ef4444;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 2000;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 小程序webview特殊适配 */
@media screen and (max-width: 414px) {
    .header {
        padding: env(safe-area-inset-top, 20px) 16px 16px;
    }

    .input-container {
        left: 12px;
        right: 12px;
        bottom: calc(env(safe-area-inset-bottom, 0px) + 16px);
    }

    .input-wrapper {
        padding: 12px 16px;
    }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .header {
        padding: 16px 12px 12px;
    }

    .title {
        font-size: 16px;
    }

    .chat-messages {
        padding: 12px;
    }

    .message-content {
        max-width: 80%;
    }

    .bot-message .message-content {
        margin-left: 22px;
    }

    .user-message .message-content {
        margin-right: 22px;
    }

    .input-container {
        left: 12px;
        right: 12px;
        bottom: 16px;
    }

    .input-wrapper {
        padding: 10px 14px;
    }

    .message-avatar {
        margin: 0 -12px;
    }
}

/* 性能优化 */
.message {
    will-change: transform;
    transform: translateZ(0);
}

.loading-spinner {
    will-change: transform;
}

.send-button {
    will-change: transform;
}

/* 触摸优化 */
.send-button,
.message-input {
    touch-action: manipulation;
}

/* 防止iOS Safari底部工具栏遮挡 */
@supports (-webkit-touch-callout: none) {
    .container {
        height: -webkit-fill-available;
    }
}

/* Markdown样式 */
.message-text h1,
.message-text h2,
.message-text h3,
.message-text h4,
.message-text h5,
.message-text h6 {
    margin: 16px 0 8px 0;
    font-weight: 600;
    line-height: 1.3;
}

.message-text h1 { font-size: 1.5em; color: #2C3E50; }
.message-text h2 { font-size: 1.3em; color: #34495E; }
.message-text h3 { font-size: 1.2em; color: #34495E; }
.message-text h4 { font-size: 1.1em; color: #34495E; }

.message-text p {
    margin: 8px 0;
    line-height: 1.6;
}

.message-text ul,
.message-text ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message-text li {
    margin: 4px 0;
    line-height: 1.5;
}

.message-text blockquote {
    margin: 12px 0;
    padding: 8px 16px;
    border-left: 4px solid #4C79FF;
    background: rgba(76, 121, 255, 0.05);
    border-radius: 0 8px 8px 0;
}

.message-text code {
    background: rgba(0, 0, 0, 0.05);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
    color: #E74C3C;
}

.message-text pre {
    background: rgba(0, 0, 0, 0.05);
    padding: 12px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.message-text pre code {
    background: none;
    padding: 0;
    color: #2C3E50;
}

.message-text strong {
    font-weight: 600;
    color: #2C3E50;
}

.message-text em {
    font-style: italic;
    color: #5D6D7E;
}

.message-text a {
    color: #4C79FF;
    text-decoration: none;
}

.message-text a:hover {
    text-decoration: underline;
}

.message-text table {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
    font-size: 14px;
}

.message-text th,
.message-text td {
    border: 1px solid #ddd;
    padding: 8px 12px;
    text-align: left;
}

.message-text th {
    background-color: rgba(76, 121, 255, 0.1);
    font-weight: 600;
}

.message-text hr {
    border: none;
    border-top: 1px solid #E5E7EB;
    margin: 16px 0;
}

/* 确保第一个元素没有上边距，最后一个元素没有下边距 */
.message-text > *:first-child {
    margin-top: 0;
}

.message-text > *:last-child {
    margin-bottom: 0;
}

/* 历史记录按钮 */
.history-button-container {
    position: absolute;
    top: 50px;
    right: 20px;
    z-index: 10;
}

.history-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.history-button:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.history-button:active {
    transform: translateY(0);
}

.history-button svg {
    flex-shrink: 0;
}

/* 历史记录弹窗 */
.history-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.history-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

.history-modal-content {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    width: 100%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
}

.history-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.history-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.history-close-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: none;
    border: none;
    border-radius: 8px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.history-modal-body {
    padding: 0;
    max-height: calc(80vh - 80px);
    overflow-y: auto;
}

.history-list {
    padding: 16px 0;
}

.history-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 40px 20px;
    color: #666;
    font-size: 14px;
}

.history-loading .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e0e0e0;
    border-top: 2px solid #4C79FF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.history-item {
    padding: 16px 24px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.history-item:hover {
    background: rgba(76, 121, 255, 0.05);
}

.history-item:last-child {
    border-bottom: none;
}

.history-item-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.history-item-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.history-item-time {
    font-size: 12px;
    color: #999;
    flex-shrink: 0;
    margin-left: 12px;
}

.history-item-preview {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.history-empty {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-size: 14px;
}
