/* global wx, marked */
class ChatApp {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.newChatButton = document.getElementById('newChatButton');
        this.chatMessages = document.getElementById('chatMessages');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.toast = document.getElementById('toast');
        this.speechBubble = document.getElementById('speechBubble');
        this.quickQuestions = document.getElementById('quickQuestions');
        this.voiceBtn = document.getElementById('voiceBtn');

        // 从配置文件获取Dify配置
        this.difyConfig = {
            ...window.DIFY_CONFIG,
            user: this.getOrCreateUserId()
        };

        this.wechatConfig = window.WECHAT_CONFIG;
        this.appConfig = window.APP_CONFIG;

        this.conversationId = null;
        this.isWechatMiniProgram = false;
        this.currentMode = 'assistant'; // 当前功能模式

        this.init();
    }
    
    async init() {
        this.checkWechatEnvironment();
        this.bindEvents();
        this.loadConversationId();

        // 清除可能无效的旧conversation_id（超过1小时的）
        this.cleanupOldConversationId();

        // 异步恢复对话历史
        await this.restoreChatHistory();
    }

    // 获取或创建持久化的用户ID
    getOrCreateUserId() {
        // 在小程序环境中，尝试从URL参数获取用户ID
        if (this.isWechatMiniProgram || typeof wx !== 'undefined') {
            const urlParams = new URLSearchParams(window.location.search);
            const userIdFromUrl = urlParams.get('user_id') || urlParams.get('openid');

            if (userIdFromUrl) {
                console.log('从URL参数获取用户ID:', userIdFromUrl);
                return window.DIFY_CONFIG.userPrefix + userIdFromUrl;
            }
        }

        // 回退到localStorage方案
        const userIdKey = 'chat_user_id';
        let userId = localStorage.getItem(userIdKey);

        if (!userId) {
            // 生成新的用户ID
            userId = window.DIFY_CONFIG.userPrefix + Date.now();
            localStorage.setItem(userIdKey, userId);
            console.log('创建新的用户ID:', userId);
        } else {
            console.log('使用已保存的用户ID:', userId);
        }

        return userId;
    }
    
    // 检查微信环境
    checkWechatEnvironment() {
        // 检查是否在微信小程序webview中
        if (typeof wx !== 'undefined' && wx.miniProgram) {
            this.isWechatMiniProgram = true;
            // 重新生成用户ID以支持小程序用户隔离
            this.difyConfig.user = this.getOrCreateUserId();
            this.initWechatMiniProgram();
        }
    }
    
    // 初始化微信小程序相关功能
    initWechatMiniProgram() {
        // 获取小程序环境信息
        if (typeof wx !== 'undefined' && wx.miniProgram) {
            wx.miniProgram.getEnv((res) => {
                if (res.miniprogram) {
                    console.log('运行在微信小程序中');

                    // 设置页面样式适配小程序
                    this.adaptForMiniProgram();

                    // 向小程序发送准备就绪消息
                    this.postMessageToMiniProgram(this.wechatConfig.messageTypes.WEBVIEW_READY, {
                        timestamp: Date.now(),
                        userAgent: navigator.userAgent,
                        screenSize: {
                            width: window.screen.width,
                            height: window.screen.height
                        }
                    });

                    // 监听页面生命周期
                    this.setupMiniProgramLifecycle();
                }
            });
        }
    }

    // 适配小程序环境
    adaptForMiniProgram() {
        // 禁用页面缩放
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });

        // 禁用双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', (e) => {
            const now = Date.now();
            if (now - lastTouchEnd <= 300) {
                e.preventDefault();
            }
            lastTouchEnd = now;
        });

        // 调整视口高度以适配小程序
        this.adjustViewportHeight();
    }

    // 调整视口高度
    adjustViewportHeight() {
        const setViewportHeight = () => {
            const vh = window.innerHeight * 0.01;
            document.documentElement.style.setProperty('--vh', `${vh}px`);
        };

        setViewportHeight();
        window.addEventListener('resize', setViewportHeight);
        window.addEventListener('orientationchange', setViewportHeight);
    }

    // 设置小程序生命周期监听
    setupMiniProgramLifecycle() {
        // 监听页面显示/隐藏
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.postMessageToMiniProgram('page-hidden', {
                    timestamp: Date.now()
                });
            } else {
                this.postMessageToMiniProgram(this.wechatConfig.messageTypes.PAGE_VISIBLE, {
                    timestamp: Date.now()
                });
            }
        });

        // 监听网络状态变化
        window.addEventListener('online', () => {
            this.postMessageToMiniProgram('network-online', {
                timestamp: Date.now()
            });
        });

        window.addEventListener('offline', () => {
            this.postMessageToMiniProgram('network-offline', {
                timestamp: Date.now()
            });
        });
    }
    
    // 向小程序发送消息
    postMessageToMiniProgram(type, data) {
        if (this.isWechatMiniProgram && typeof wx !== 'undefined' && wx.miniProgram) {
            wx.miniProgram.postMessage({
                data: {
                    type: type,
                    data: data
                }
            });
        }
    }
    
    // 绑定事件
    bindEvents() {
        // 发送按钮点击事件
        this.sendButton.addEventListener('click', () => {
            this.sendMessage();
        });

        // 新对话按钮点击事件
        if (this.newChatButton) {
            this.newChatButton.addEventListener('click', () => {
                this.startNewConversation();
            });
        }

        // 输入框回车事件
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 输入框内容变化事件
        this.messageInput.addEventListener('input', () => {
            this.updateSendButton();
            this.updateCharCount();
        });

        // 功能按钮切换
        document.querySelectorAll('.function-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchMode(e.target.dataset.type);
            });
        });

        // 快捷问题点击
        document.querySelectorAll('.quick-question-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const question = e.target.dataset.question;
                this.selectQuickQuestion(question);
            });
        });

        // 语音按钮（暂时只是占位）
        if (this.voiceBtn) {
            this.voiceBtn.addEventListener('click', () => {
                this.showToast('语音功能开发中...');
            });
        }

        // 历史记录按钮
        this.historyButton = document.getElementById('historyButton');
        this.historyModal = document.getElementById('historyModal');
        this.historyModalOverlay = document.getElementById('historyModalOverlay');
        this.historyCloseBtn = document.getElementById('historyCloseBtn');
        this.historyList = document.getElementById('historyList');

        if (this.historyButton) {
            this.historyButton.addEventListener('click', () => {
                this.showHistoryModal();
            });
        }

        if (this.historyCloseBtn) {
            this.historyCloseBtn.addEventListener('click', () => {
                this.hideHistoryModal();
            });
        }

        if (this.historyModalOverlay) {
            this.historyModalOverlay.addEventListener('click', () => {
                this.hideHistoryModal();
            });
        }

        // 防止页面滚动
        document.addEventListener('touchmove', (e) => {
            if (e.target === this.messageInput) return;
            e.preventDefault();
        }, { passive: false });
    }
    

    
    // 更新发送按钮状态
    updateSendButton() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = !hasText;
    }
    
    // 更新字符计数
    updateCharCount() {
        const count = this.messageInput.value.length;
        const charCountElement = document.querySelector('.char-count');

        // 检查元素是否存在，避免null错误
        if (charCountElement) {
            charCountElement.textContent = `${count}/1000`;

            if (count > 900) {
                charCountElement.style.color = '#ef4444';
            } else {
                charCountElement.style.color = '#94a3b8';
            }
        }
    }
    
    // 发送消息
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;
        
        // 添加用户消息到界面
        this.addMessage(message, 'user');
        
        // 清空输入框
        this.messageInput.value = '';
        this.updateSendButton();
        this.updateCharCount();
        
        // 显示加载状态
        this.showLoading(true);
        
        try {
            // 添加调试信息
            console.log('发送消息:', message);
            console.log('API配置:', this.difyConfig);

            // 调用Dify API
            const response = await this.callDifyAPI(message);

            // 添加调试信息
            console.log('API响应:', response);

            // 添加AI回复到界面
            if (response && response.answer) {
                // 清理回复内容，移除"Final Answer:"前缀
                let cleanAnswer = this.cleanBotResponse(response.answer);
                this.addMessage(cleanAnswer, 'bot');

                // 保存对话ID
                if (response.conversation_id) {
                    console.log('收到新的conversation_id:', response.conversation_id);
                    this.conversationId = response.conversation_id;
                    this.saveConversationId();
                    console.log('已保存conversation_id到localStorage');
                } else {
                    console.log('响应中没有conversation_id');
                }
            } else {
                throw new Error('API返回格式错误');
            }
            
        } catch (error) {
            console.error('发送消息失败:', error);
            this.showToast('发送失败，请稍后重试');
            this.addMessage('抱歉，我暂时无法回复您的消息，请稍后重试。', 'bot');
        } finally {
            this.showLoading(false);
        }
    }
    
    // 调用HTTP服务器API
    async callDifyAPI(message) {
        let requestBody = {
            message: message,
            user_id: this.difyConfig.user
        };

        // 如果有对话ID，添加到请求中
        if (this.conversationId) {
            requestBody.conversation_id = this.conversationId;
            console.log('使用已有的conversation_id:', this.conversationId);
        } else {
            console.log('这是新对话，没有conversation_id');
        }

        // 添加重试机制
        let lastError;
        for (let i = 0; i < this.difyConfig.maxRetries; i++) {
            try {
                console.log(`第${i + 1}次尝试调用API:`, this.difyConfig.apiUrl);
                console.log('请求体:', requestBody);

                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.difyConfig.timeout);

                const response = await fetch(this.difyConfig.apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody),
                    signal: controller.signal
                });

                console.log('响应状态:', response.status, response.statusText);

                clearTimeout(timeoutId);

                if (!response.ok) {
                    // 如果是500错误且有conversation_id，可能是conversation_id无效，清除它并重试
                    if (response.status === 500 && this.conversationId && i === 0) {
                        console.log('检测到500错误，清除可能无效的conversation_id并重试');
                        this.clearConversationId();
                        // 更新请求体，移除conversation_id
                        requestBody = {
                            message: message,
                            user_id: this.difyConfig.user
                        };
                    }
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                // 保存对话历史
                if (this.appConfig.autoSaveHistory) {
                    this.saveChatHistory(message, result.answer);
                }

                return result;

            } catch (error) {
                lastError = error;
                if (this.difyConfig.debug) {
                    console.log(`API调用失败，第${i + 1}次重试:`, error);
                }

                // 如果不是最后一次重试，等待一段时间后重试
                if (i < this.difyConfig.maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
                }
            }
        }

        throw lastError;
    }

    // 保存对话历史
    saveChatHistory(userMessage, botMessage) {
        try {
            const history = this.getChatHistory();
            history.push({
                user: userMessage,
                bot: botMessage,
                timestamp: Date.now()
            });

            // 限制历史记录数量（最多保存100条）
            if (history.length > 100) {
                history.splice(0, history.length - 100);
            }

            localStorage.setItem(this.appConfig.storageKeys.chatHistory, JSON.stringify(history));
        } catch (error) {
            console.warn('保存对话历史失败:', error);
        }
    }

    // 获取对话历史
    getChatHistory() {
        try {
            const history = localStorage.getItem(this.appConfig.storageKeys.chatHistory);
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.warn('获取对话历史失败:', error);
            return [];
        }
    }

    // 加载保存的对话ID
    loadConversationId() {
        try {
            const savedId = localStorage.getItem(this.appConfig.storageKeys.conversationId);
            if (savedId) {
                this.conversationId = savedId;
                console.log('从localStorage加载conversation_id:', savedId);
            } else {
                console.log('localStorage中没有保存的conversation_id');
            }
        } catch (error) {
            console.warn('加载对话ID失败:', error);
        }
    }

    // 保存对话ID
    saveConversationId() {
        try {
            if (this.conversationId) {
                localStorage.setItem(this.appConfig.storageKeys.conversationId, this.conversationId);
                localStorage.setItem(this.appConfig.storageKeys.conversationId + '_timestamp', Date.now().toString());
            }
        } catch (error) {
            console.warn('保存对话ID失败:', error);
        }
    }

    // 清除对话ID（用于开始新对话）
    clearConversationId() {
        try {
            this.conversationId = null;
            localStorage.removeItem(this.appConfig.storageKeys.conversationId);
            localStorage.removeItem(this.appConfig.storageKeys.conversationId + '_timestamp');
            console.log('已清除conversation_id，开始新对话');
        } catch (error) {
            console.warn('清除对话ID失败:', error);
        }
    }

    // 清除可能无效的旧conversation_id
    cleanupOldConversationId() {
        try {
            const timestampKey = this.appConfig.storageKeys.conversationId + '_timestamp';
            const timestamp = localStorage.getItem(timestampKey);

            if (timestamp) {
                const age = Date.now() - parseInt(timestamp);
                const oneWeek = 7 * 24 * 60 * 60 * 1000; // 7天

                if (age > oneWeek) {
                    console.log('检测到超过7天的旧conversation_id，自动清除');
                    this.clearConversationId();
                }
            }
        } catch (error) {
            console.warn('清理旧对话ID失败:', error);
        }
    }

    // 开始新对话
    startNewConversation() {
        // 清除对话ID
        this.clearConversationId();

        // 清除聊天历史
        this.chatMessages.innerHTML = `
            <div class="message bot-message">
                <div class="message-avatar">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0Qzc5RkYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+" alt="AI助手">
                </div>
                <div class="message-content">
                    <div class="message-text">
                        我是联通数科矛盾调解小助手，我会根据您描述的事件信息，参考相应的法律条文，为您输出调解方案。请给出矛盾的双方的姓名！
                    </div>
                </div>
            </div>
        `;

        // 清除localStorage中的聊天历史
        try {
            localStorage.removeItem(this.appConfig.storageKeys.chatHistory);
        } catch (error) {
            console.warn('清除聊天历史失败:', error);
        }

        console.log('新对话已开始');
    }

    // 恢复对话历史
    async restoreChatHistory() {
        // 如果有保存的对话ID，尝试恢复对话历史
        if (this.conversationId) {
            try {
                console.log('尝试恢复对话历史，conversation_id:', this.conversationId);

                // 获取用户ID - 使用已存在的用户ID
                const userId = this.difyConfig.user;

                // 构建API URL
                const baseUrl = window.location.protocol === 'file:' ? 'http://localhost:5000' : '';
                const response = await fetch(`${baseUrl}/api/conversations/${this.conversationId}/messages?user_id=${userId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success && result.messages && result.messages.length > 0) {
                    console.log('成功恢复历史对话，消息数量:', result.messages.length);
                    this.renderHistoryMessages(result.messages);
                } else {
                    console.log('没有找到历史消息或消息为空');
                }
            } catch (error) {
                console.warn('恢复对话历史失败:', error);
                // 如果恢复失败，清除无效的对话ID
                this.conversationId = null;
                localStorage.removeItem(this.appConfig.storageKeys.conversationId);
                localStorage.removeItem(this.appConfig.storageKeys.conversationId + '_timestamp');
            }
        } else {
            console.log('没有保存的对话ID，开始新对话');
        }
    }

    // 切换功能模式
    switchMode(mode) {
        this.currentMode = mode;

        // 更新按钮状态
        document.querySelectorAll('.function-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-type="${mode}"]`).classList.add('active');

        // 更新机器人对话气泡
        this.updateSpeechBubble(mode);

        // 更新快捷问题
        this.updateQuickQuestions(mode);

        console.log('切换到模式:', mode);
    }

    // 更新机器人对话气泡
    updateSpeechBubble(mode) {
        const messages = {
            assistant: {
                title: 'Hi,您可以这样向我提交平台应用的相关信息！',
                subtitle: '例如：我对有多少应用？'
            },
            party: {
                title: '欢迎来到AI党史学习！',
                subtitle: '例如：请介绍一下中国共产党的历史'
            },
            qa: {
                title: '智能问答为您服务！',
                subtitle: '例如：请问如何办理相关手续？'
            },
            policy: {
                title: '涉农政策咨询助手！',
                subtitle: '例如：最新的农业补贴政策有哪些？'
            }
        };

        const message = messages[mode] || messages.assistant;
        this.speechBubble.innerHTML = `
            <p>${message.title}</p>
            <p>${message.subtitle}</p>
        `;
    }

    // 更新快捷问题
    updateQuickQuestions(mode) {
        const questions = {
            assistant: [
                '我想购买水卡，怎么买？',
                '我想找份工作，可以从哪找？',
                '我想看一些党员资料，从哪看？',
                '我身体有点不舒服，该怎么办？',
                '惠农服务有哪些应用？'
            ],
            party: [
                '中国共产党成立于哪一年？',
                '长征的历史意义是什么？',
                '改革开放的重要意义',
                '新时代的主要特征',
                '党的二十大主要内容'
            ],
            qa: [
                '如何办理身份证？',
                '医保报销流程是什么？',
                '如何申请低保？',
                '房产证办理需要什么材料？',
                '如何查询社保缴费记录？'
            ],
            policy: [
                '最新农业补贴政策',
                '农村土地承包政策',
                '农民工返乡创业政策',
                '农村医疗保险政策',
                '农村教育扶持政策'
            ]
        };

        const modeQuestions = questions[mode] || questions.assistant;
        this.quickQuestions.innerHTML = modeQuestions.map(question =>
            `<div class="quick-question-item" data-question="${question}">💬 ${question}</div>`
        ).join('');

        // 重新绑定快捷问题点击事件
        this.quickQuestions.querySelectorAll('.quick-question-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const question = e.target.dataset.question;
                this.selectQuickQuestion(question);
            });
        });
    }

    // 选择快捷问题
    selectQuickQuestion(question) {
        this.messageInput.value = question;
        this.updateSendButton();

        // 隐藏快捷问题，显示聊天区域
        this.quickQuestions.classList.add('hidden');
        this.chatMessages.classList.remove('hidden');

        // 自动发送消息
        setTimeout(() => {
            this.sendMessage();
        }, 300);
    }
    
    // 添加消息到聊天界面
    addMessage(text, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;
        
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        
        if (type === 'user') {
            avatarDiv.textContent = '我';
        } else {
            avatarDiv.innerHTML = '<img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0Qzc5RkYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+" alt="AI助手">';
        }
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        const textDiv = document.createElement('div');
        textDiv.className = 'message-text';

        // 如果是AI回复，使用Markdown渲染
        if (type === 'bot' && typeof marked !== 'undefined') {
            try {
                textDiv.innerHTML = marked.parse(text);
            } catch (error) {
                console.warn('Markdown渲染失败，使用纯文本:', error);
                textDiv.textContent = text;
            }
        } else {
            textDiv.textContent = text;
        }

        contentDiv.appendChild(textDiv);
        
        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);
        
        this.chatMessages.appendChild(messageDiv);
        
        // 滚动到底部
        this.scrollToBottom();
        
        // 向小程序发送消息更新通知
        this.postMessageToMiniProgram('message-sent', {
            type: type,
            text: text,
            timestamp: Date.now()
        });
    }
    
    // 格式化时间
    formatTime(date) {
        const now = new Date();

        if (date.getFullYear() === now.getFullYear()) { // 今年
            // 显示：8月21日 14:30
            const month = date.getMonth() + 1;
            const day = date.getDate();
            const hour = date.getHours().toString().padStart(2, '0');
            const minute = date.getMinutes().toString().padStart(2, '0');
            return `${month}月${day}日 ${hour}:${minute}`;
        } else { // 不是今年
            // 显示：2024年8月21日 14:30
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            const hour = date.getHours().toString().padStart(2, '0');
            const minute = date.getMinutes().toString().padStart(2, '0');
            return `${year}年${month}月${day}日 ${hour}:${minute}`;
        }
    }
    
    // 滚动到底部
    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }
    
    // 显示/隐藏加载状态
    showLoading(show) {
        if (show) {
            this.loadingOverlay.classList.remove('hidden');
        } else {
            this.loadingOverlay.classList.add('hidden');
        }
    }

    // 清理AI回复内容
    cleanBotResponse(answer) {
        if (!answer) return answer;

        console.log('原始回复内容:', JSON.stringify(answer));

        let cleaned = answer;

        // 处理JSON格式的工具调用结果
        try {
            // 检查是否包含JSON格式的工具调用
            const jsonMatch = cleaned.match(/\{[\s\S]*?"action"[\s\S]*?\}/);
            if (jsonMatch) {
                const jsonStr = jsonMatch[0];
                const toolCall = JSON.parse(jsonStr);

                if (toolCall.action && toolCall.action.includes('dataset_')) {
                    // 这是知识库查询的工具调用，隐藏JSON内容
                    cleaned = cleaned.replace(jsonStr, '').trim();

                    // 如果清理后内容为空或只剩下换行符，提供默认提示
                    if (!cleaned || cleaned.match(/^\s*$/)) {
                        cleaned = '正在查询相关信息...';
                    }
                }
            }
        } catch {
            // JSON解析失败，继续其他清理逻辑
            console.log('JSON解析失败，继续常规清理');
        }

        // 移除"Final Answer:"前缀（支持中英文）
        cleaned = cleaned.replace(/^Final Answer:\s*/i, '');
        cleaned = cleaned.replace(/^最终答案:\s*/i, '');
        cleaned = cleaned.replace(/^最终回答:\s*/i, '');

        // 移除其他可能的前缀
        cleaned = cleaned.replace(/^Answer:\s*/i, '');
        cleaned = cleaned.replace(/^回答:\s*/i, '');

        // 去除首尾空白
        cleaned = cleaned.trim();

        console.log('清理后内容:', JSON.stringify(cleaned));

        return cleaned || answer; // 如果清理后为空，返回原内容
    }

    // 显示Toast提示
    showToast(message) {
        const toastMessage = this.toast.querySelector('.toast-message');
        toastMessage.textContent = message;
        this.toast.classList.remove('hidden');

        setTimeout(() => {
            this.toast.classList.add('hidden');
        }, 3000);
    }

    // 显示历史记录弹窗
    async showHistoryModal() {
        try {
            this.historyModal.classList.remove('hidden');
            this.historyList.innerHTML = `
                <div class="history-loading">
                    <div class="spinner"></div>
                    <span>加载历史记录中...</span>
                </div>
            `;

            // 获取用户ID - 使用已存在的用户ID
            const userId = this.difyConfig.user;

            // 调用API获取会话列表
            const baseUrl = window.location.protocol === 'file:' ? 'http://localhost:5000' : '';
            const response = await fetch(`${baseUrl}/api/conversations/${userId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.conversations && result.conversations.length > 0) {
                this.renderHistoryList(result.conversations);
            } else {
                this.historyList.innerHTML = `
                    <div class="history-empty">
                        <p>暂无历史对话记录</p>
                    </div>
                `;
            }

        } catch (error) {
            console.error('加载历史记录失败:', error);
            this.historyList.innerHTML = `
                <div class="history-empty">
                    <p>加载历史记录失败</p>
                    <p style="font-size: 12px; color: #999; margin-top: 8px;">${error.message}</p>
                </div>
            `;
        }
    }

    // 隐藏历史记录弹窗
    hideHistoryModal() {
        this.historyModal.classList.add('hidden');
    }

    // 渲染历史记录列表
    renderHistoryList(conversations) {
        // 按时间倒序排列，只显示最近5条
        const sortedConversations = conversations
            .sort((a, b) => new Date(b.created_at * 1000) - new Date(a.created_at * 1000))
            .slice(0, 5);

        if (sortedConversations.length === 0) {
            this.historyList.innerHTML = `
                <div class="history-empty">
                    <p>暂无历史对话记录</p>
                </div>
            `;
            return;
        }

        const historyHTML = sortedConversations.map(conversation => {
            // API返回的是秒级时间戳，需要转换为毫秒级
            const createdTime = new Date(conversation.created_at * 1000);
            const timeStr = this.formatTime(createdTime);

            // 获取对话标题
            let title = conversation.name || `对话 ${conversation.id.substring(0, 8)}`;
            // 限制标题长度
            if (title.length > 20) {
                title = title.substring(0, 20) + '...';
            }

            // 获取预览内容 - 优先使用summary，如果没有则使用introduction的前50个字符
            let preview = '';
            if (conversation.summary && conversation.summary.trim()) {
                preview = conversation.summary.length > 50 ?
                         conversation.summary.substring(0, 50) + '...' :
                         conversation.summary;
            } else if (conversation.introduction && conversation.introduction.trim()) {
                // 从introduction中提取有用信息，跳过重复的开头
                const intro = conversation.introduction.trim();
                if (intro.includes('矛盾调解小助手')) {
                    // 如果是标准的介绍文本，显示更简洁的预览
                    preview = '矛盾调解对话';
                } else {
                    preview = intro.length > 50 ? intro.substring(0, 50) + '...' : intro;
                }
            } else {
                preview = '点击查看完整对话内容';
            }

            return `
                <div class="history-item" data-conversation-id="${conversation.id}">
                    <div class="history-item-header">
                        <div class="history-item-title">${this.escapeHtml(title)}</div>
                        <div class="history-item-time">${timeStr}</div>
                    </div>
                    <div class="history-item-preview">${this.escapeHtml(preview)}</div>
                </div>
            `;
        }).join('');

        this.historyList.innerHTML = historyHTML;

        // 绑定点击事件
        this.historyList.querySelectorAll('.history-item').forEach(item => {
            item.addEventListener('click', () => {
                const conversationId = item.dataset.conversationId;
                this.loadHistoryConversation(conversationId);
            });
        });
    }

    // 加载历史对话
    async loadHistoryConversation(conversationId) {
        try {
            this.hideHistoryModal();
            this.showLoading(true);

            // 获取用户ID - 使用已存在的用户ID
            const userId = this.difyConfig.user;

            // 调用API获取会话消息
            const baseUrl = window.location.protocol === 'file:' ? 'http://localhost:5000' : '';
            const response = await fetch(`${baseUrl}/api/conversations/${conversationId}/messages?user_id=${userId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            if (result.success && result.messages) {
                // 清空当前对话
                this.chatMessages.innerHTML = '';

                // 设置当前会话ID
                this.conversationId = conversationId;
                this.saveConversationId();

                // 渲染历史消息
                this.renderHistoryMessages(result.messages);
            } else {
                throw new Error(result.error || '获取历史消息失败');
            }

        } catch (error) {
            console.error('加载历史对话失败:', error);
            this.showToast('加载历史对话失败');
        } finally {
            this.showLoading(false);
        }
    }

    // 渲染历史消息
    renderHistoryMessages(messages) {
        // 按时间正序排列消息
        const sortedMessages = messages.sort((a, b) => new Date(a.created_at * 1000) - new Date(b.created_at * 1000));

        sortedMessages.forEach(message => {
            // 添加用户消息
            if (message.query) {
                this.addMessage(message.query, 'user');
            }

            // 添加AI回复 - 检查多个可能的字段
            let aiResponse = '';

            // 优先使用answer字段
            if (message.answer && message.answer.trim()) {
                aiResponse = message.answer;
                console.log('使用message.answer:', aiResponse);
            }
            // 如果没有answer，检查agent_thoughts
            else if (message.agent_thoughts && message.agent_thoughts.length > 0) {
                const thought = message.agent_thoughts[0];
                console.log('agent_thoughts内容:', thought);

                // 检查thought对象的不同字段
                if (thought.thought && thought.thought.trim()) {
                    let thoughtContent = thought.thought;
                    // 清理可能的英文前缀
                    thoughtContent = thoughtContent.replace(/^I am thinking about how to help you\.?\s*/i, '');
                    thoughtContent = thoughtContent.replace(/^Let me help you with that\.?\s*/i, '');

                    if (thoughtContent.trim()) {
                        aiResponse = thoughtContent;
                        console.log('使用清理后的thought:', aiResponse);
                    }
                }
                // 检查是否有observation字段（工具调用结果）
                else if (thought.observation && thought.observation.trim()) {
                    aiResponse = thought.observation;
                    console.log('使用observation:', aiResponse);
                }
            }
            // 检查是否有response字段
            else if (message.response && message.response.trim()) {
                aiResponse = message.response;
                console.log('使用message.response:', aiResponse);
            }

            if (aiResponse && aiResponse.trim()) {
                const cleanAnswer = this.cleanBotResponse(aiResponse);
                this.addMessage(cleanAnswer, 'bot');
            } else {
                console.warn('未找到有效的AI回复内容，消息结构:', message);
            }
        });

        // 滚动到底部
        setTimeout(() => {
            this.scrollToBottom();
        }, 100);
    }

    // HTML转义函数，防止XSS攻击
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

}

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});

// 处理页面可见性变化
document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
        // 页面变为可见时，向小程序发送通知
        if (typeof wx !== 'undefined' && wx.miniProgram) {
            wx.miniProgram.postMessage({
                data: {
                    type: 'page-visible',
                    timestamp: Date.now()
                }
            });
        }
    }
});
