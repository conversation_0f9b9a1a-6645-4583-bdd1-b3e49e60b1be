#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Dify Agent聊天程序
专注于稳定的阻塞式响应
"""

import requests
import json
import uuid

class SimpleDifyClient:
    """简化版Dify客户端，只支持阻塞式响应"""
    
    def __init__(self, api_base_url: str, api_key: str):
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })
        
    def send_message(self, query: str, user_id: str = None, conversation_id: str = None):
        """发送消息并获取回复"""
        
        if user_id is None:
            user_id = f"user-{uuid.uuid4().hex[:8]}"
            
        url = f"{self.api_base_url}/chat-messages"
        
        payload = {
            "inputs": {},
            "query": query,
            "response_mode": "streaming",
            "user": user_id
        }
        
        if conversation_id:
            payload["conversation_id"] = conversation_id
            
        try:
            print("正在发送请求...")

            response = self.session.post(url, json=payload, stream=True, timeout=60)
            response.raise_for_status()

            full_response = ""
            conversation_id = None
            message_id = None

            print("AI: ", end='', flush=True)

            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀

                        if data_str.strip() == '[DONE]':
                            break

                        try:
                            data = json.loads(data_str)
                            event = data.get('event', '')

                            if event == 'agent_message':
                                # 处理AI消息事件
                                answer = data.get('answer', '')
                                full_response += answer
                                print(answer, end='', flush=True)
                            elif event == 'message':
                                # 处理完整消息事件
                                answer = data.get('answer', '')
                                if answer:
                                    full_response = answer
                                    print(answer, end='', flush=True)

                                # 保存对话信息
                                if not conversation_id:
                                    conversation_id = data.get('conversation_id')
                                if not message_id:
                                    message_id = data.get('id')

                            elif event == 'message_end':
                                # 消息结束事件
                                break

                        except json.JSONDecodeError:
                            # 忽略无法解析的行
                            continue

            print()  # 换行

            return {
                'answer': full_response,
                'conversation_id': conversation_id,
                'message_id': message_id,
                'success': True
            }

        except requests.exceptions.RequestException as e:
            return {
                'answer': f'请求失败: {e}',
                'success': False
            }
        except json.JSONDecodeError as e:
            return {
                'answer': f'响应解析失败: {e}',
                'success': False
            }
        except Exception as e:
            return {
                'answer': f'未知错误: {e}',
                'success': False
            }


def main():
    """主函数"""
    
    # 配置信息
    API_BASE_URL = "http://117.72.181.138:8088/v1"
    API_KEY = "app-AaFO7cdbloXPGTYv43M55k4U"
    
    # 创建客户端
    client = SimpleDifyClient(API_BASE_URL, API_KEY)
    
    print("=== 简化版 Dify Agent 对话 ===")
    print("输入 'quit' 或 'exit' 退出程序")
    print("输入 'test' 运行连接测试")
    print("-" * 40)
    
    user_id = f"user-{uuid.uuid4().hex[:8]}"
    conversation_id = None
    
    while True:
        try:
            # 获取用户输入
            try:
                user_input = input("\n你: ").strip()
            except EOFError:
                print("\n\n检测到输入结束，程序退出。")
                break
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
                
            if user_input.lower() == 'test':
                print("正在测试API连接...")
                result = client.send_message("你好", user_id)
                if result['success']:
                    print(f"\n✓ 连接测试成功")
                    if result['conversation_id']:
                        conversation_id = result['conversation_id']
                else:
                    print(f"✗ 连接测试失败: {result['answer']}")
                continue
                
            if not user_input:
                continue
                
            # 发送消息
            result = client.send_message(user_input, user_id, conversation_id)

            if result['success']:
                # 流式响应已经在send_message中显示了，这里只需要保存对话ID
                if result['conversation_id']:
                    conversation_id = result['conversation_id']
            else:
                print(f"\n错误: {result['answer']}")
                
        except Exception as e:
            print(f"\n发生错误: {e}")
            # 如果是严重错误，可以选择退出
            if "EOF" in str(e):
                print("检测到输入流问题，程序退出。")
                break


def quick_test():
    """快速测试函数"""
    
    API_BASE_URL = "http://117.72.181.138:8088/v1"
    API_KEY = "app-AaFO7cdbloXPGTYv43M55k4U"
    
    client = SimpleDifyClient(API_BASE_URL, API_KEY)
    
    print("=== 快速API测试 ===")
    
    # 测试问题列表
    test_questions = [
        "你好",
        "你是谁？",
        "什么是矛盾调解？",
        "张三和李四发生合同纠纷，请给出调解建议"
    ]
    
    user_id = f"test-user-{uuid.uuid4().hex[:8]}"
    conversation_id = None
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. 测试问题: {question}")
        
        result = client.send_message(question, user_id, conversation_id)
        
        if result['success']:
            print(f"\n   ✓ 成功")
            if result['conversation_id']:
                conversation_id = result['conversation_id']
        else:
            print(f"   ✗ 失败: {result['answer']}")
            
    print("\n测试完成！")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'test':
        quick_test()
    else:
        main()
