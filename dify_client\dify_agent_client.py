#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dify Agent API 调用程序
基于提供的API文档和配置信息
"""

import requests
import json
import uuid
import time
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DifyAgentClient:
    """Dify Agent API 客户端"""
    
    def __init__(self, api_base_url: str, api_key: str):
        """
        初始化客户端
        
        Args:
            api_base_url: API基础URL，例如 "http://**************:8088/v1"
            api_key: API密钥，例如 "app-AaFO7cdbloXPGTYv43M55k4U"
        """
        self.api_base_url = api_base_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        })
        
    def send_chat_message(self, 
                         query: str, 
                         user_id: str = None,
                         conversation_id: str = None,
                         inputs: Dict[str, Any] = None,
                         response_mode: str = "streaming",
                         files: List[Dict] = None) -> Dict:
        """
        发送聊天消息
        
        Args:
            query: 用户输入的问题
            user_id: 用户ID，如果不提供会自动生成
            conversation_id: 对话ID，用于继续之前的对话
            inputs: 额外的输入参数
            response_mode: 响应模式，"streaming" 或 "blocking"
            files: 文件列表，格式如图片中所示
            
        Returns:
            API响应结果
        """
        if user_id is None:
            user_id = f"user-{uuid.uuid4().hex[:8]}"
            
        if inputs is None:
            inputs = {}
            
        url = f"{self.api_base_url}/chat-messages"
        
        payload = {
            "inputs": inputs,
            "query": query,
            "response_mode": response_mode,
            "user": user_id
        }
        
        # 如果有对话ID，添加到请求中
        if conversation_id:
            payload["conversation_id"] = conversation_id
            
        # 如果有文件，添加到请求中
        if files:
            payload["files"] = files
            
        try:
            logger.info(f"发送请求到: {url}")
            logger.info(f"请求参数: {json.dumps(payload, ensure_ascii=False, indent=2)}")
            
            if response_mode == "streaming":
                return self._handle_streaming_response(url, payload)
            else:
                response = self.session.post(url, json=payload, timeout=60)
                response.raise_for_status()
                return response.json()
                
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {e}")
            raise
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            raise
            
    def _handle_streaming_response(self, url: str, payload: Dict) -> Dict:
        """
        处理流式响应
        
        Args:
            url: 请求URL
            payload: 请求参数
            
        Returns:
            完整的响应结果
        """
        try:
            response = self.session.post(url, json=payload, stream=True, timeout=60)
            response.raise_for_status()
            
            full_response = ""
            conversation_id = None
            message_id = None
            
            logger.info("开始接收流式响应...")
            
            for line in response.iter_lines():
                if line:
                    line_str = line.decode('utf-8')
                    if line_str.startswith('data: '):
                        data_str = line_str[6:]  # 移除 'data: ' 前缀
                        
                        if data_str.strip() == '[DONE]':
                            logger.info("流式响应结束")
                            break
                            
                        try:
                            data = json.loads(data_str)
                            event = data.get('event', '')

                            # 添加调试信息
                            logger.info(f"事件类型: {event}")
                            logger.info(f"数据内容: {data}")

                            # 在所有事件中检查conversation_id和message_id
                            if not conversation_id and data.get('conversation_id'):
                                conversation_id = data.get('conversation_id')
                                logger.info(f"从{event}事件获取conversation_id: {conversation_id}")
                            if not message_id and data.get('id'):
                                message_id = data.get('id')
                                logger.info(f"从{event}事件获取message_id: {message_id}")

                            if event == 'agent_message':
                                # 处理AI消息事件
                                answer = data.get('answer', '')
                                full_response += answer
                                print(answer, end='', flush=True)
                            elif event == 'message':
                                # 处理完整消息事件
                                answer = data.get('answer', '')
                                if answer:
                                    full_response = answer
                                    print(answer, end='', flush=True)

                            elif event == 'message_end':
                                # 消息结束事件
                                logger.info("\n消息接收完成")
                                metadata = data.get('metadata', {})
                                usage = metadata.get('usage', {})
                                logger.info(f"Token使用情况: {usage}")
                                
                            elif event == 'error':
                                # 错误事件
                                error_msg = data.get('message', '未知错误')
                                logger.error(f"API返回错误: {error_msg}")
                                raise Exception(f"API错误: {error_msg}")
                                
                        except json.JSONDecodeError:
                            # 忽略无法解析的行
                            continue
                            
            return {
                'answer': full_response,
                'conversation_id': conversation_id,
                'message_id': message_id
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"流式请求失败: {e}")
            raise
            
    def get_conversation_messages(self, conversation_id: str, user_id: str = None) -> Dict:
        """
        获取对话历史消息
        
        Args:
            conversation_id: 对话ID
            user_id: 用户ID
            
        Returns:
            对话历史
        """
        if user_id is None:
            user_id = f"user-{uuid.uuid4().hex[:8]}"
            
        url = f"{self.api_base_url}/messages"
        params = {
            'conversation_id': conversation_id,
            'user': user_id
        }
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"获取对话历史失败: {e}")
            raise
            
    def get_conversations(self, user_id: str = None, limit: int = 20) -> Dict:
        """
        获取对话列表
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
            
        Returns:
            对话列表
        """
        if user_id is None:
            user_id = f"user-{uuid.uuid4().hex[:8]}"
            
        url = f"{self.api_base_url}/conversations"
        params = {
            'user': user_id,
            'limit': limit
        }
        
        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"获取对话列表失败: {e}")
            raise


def main():
    """主函数 - 演示如何使用DifyAgentClient"""
    
    # 配置信息
    API_BASE_URL = "http://**************:8088/v1"
    API_KEY = "app-AaFO7cdbloXPGTYv43M55k4U"
    
    # 创建客户端
    client = DifyAgentClient(API_BASE_URL, API_KEY)
    
    print("=== Dify Agent 对话测试 ===")
    print("输入 'quit' 或 'exit' 退出程序")
    print("输入 'history' 查看对话历史")
    print("-" * 50)

    # 响应模式设置（这个Agent只支持流式响应）
    response_mode = "streaming"
    print(f"当前响应模式: {response_mode} (流式响应)")
    print("-" * 50)
    
    user_id = f"user-{uuid.uuid4().hex[:8]}"
    conversation_id = None
    
    while True:
        try:
            # 获取用户输入
            try:
                user_input = input("\n你: ").strip()
            except EOFError:
                print("\n\n检测到输入结束，程序退出。")
                break
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break

            if user_input.lower() in ['quit', 'exit', '退出']:
                print("再见！")
                break
                
            if user_input.lower() == 'history':
                if conversation_id:
                    print("\n=== 对话历史 ===")
                    history = client.get_conversation_messages(conversation_id, user_id)
                    for msg in history.get('data', []):
                        print(f"用户: {msg.get('query', '')}")
                        print(f"AI: {msg.get('answer', '')}")
                        print("-" * 30)
                else:
                    print("还没有对话历史")
                continue


                
            if not user_input:
                continue
                
            # 发送消息
            print("\nAI: ", end='', flush=True)

            result = client.send_chat_message(
                query=user_input,
                user_id=user_id,
                conversation_id=conversation_id,
                response_mode=response_mode
            )
            
            # 保存对话ID用于后续对话
            if result.get('conversation_id'):
                conversation_id = result['conversation_id']
                
        except Exception as e:
            logger.error(f"发生错误: {e}")
            print(f"\n发生错误: {e}")
            # 如果是严重错误，可以选择退出
            if "EOF" in str(e):
                print("检测到输入流问题，程序退出。")
                break


if __name__ == "__main__":
    main()
