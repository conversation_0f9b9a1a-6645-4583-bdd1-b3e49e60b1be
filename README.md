# 矛盾调解AI助手项目

这是一个基于Dify API的矛盾调解AI助手项目，提供Python客户端和H5前端两种使用方式。

## 📁 项目结构

```
├── miniprogram_h5/        # H5前端（推荐使用）
│   ├── dify_http_server.py # 集成服务器（一键启动）
│   ├── index.html         # H5主页面
│   ├── styles.css         # 样式文件
│   ├── script.js          # JavaScript逻辑
│   ├── config.js          # 前端配置
│   └── assets/            # 资源文件
│       ├── background.png # 背景图片
│       └── tongtong.gif   # 机器人头像
├── dify_client/           # Python客户端（备用）
│   ├── simple_chat.py     # 简化版聊天程序
│   ├── dify_agent_client.py # 完整功能客户端
│   └── requirements.txt   # Python依赖
└── README.md              # 项目说明（本文件）
```

## 🚀 快速开始

### H5前端（推荐）

**超简单一键启动：**

```bash
cd miniprogram_h5
python dify_http_server.py
```

启动后自动：
- ✅ 启动HTTP API服务器
- ✅ 提供H5静态文件服务
- ✅ 打开浏览器访问 http://localhost:5000

### Python客户端（备用）

```bash
cd dify_client
pip install -r requirements.txt
python simple_chat.py
```

## 🎯 主要功能

- **矛盾调解咨询**：专业的矛盾调解建议
- **多轮对话**：支持连续对话，保持上下文
- **流式响应**：实时显示AI回复
- **H5界面**：美观的聊天界面，支持移动端
- **小程序支持**：可嵌入微信小程序
- **一键启动**：集成服务器，无需复杂配置

## 🔧 配置信息

- **API服务器**: http://**************:8088/v1
- **API Key**: app-AaFO7cdbloXPGTYv43M55k4U
- **应用类型**: Agent Chat App（仅支持流式响应）
- **本地服务**: http://localhost:5000

## 📝 使用示例

### H5前端使用
1. 启动服务器：`python dify_http_server.py`
2. 打开浏览器访问：http://localhost:5000
3. 在界面中输入问题，与AI助手对话

### Python客户端使用
```python
# 进入dify_client目录
cd dify_client
python simple_chat.py

# 在命令行中与AI助手对话
你: 张三和李四发生合同纠纷，请给出调解建议
AI: [AI会提供专业的调解建议]
```

## 🎉 特色

- ✅ **一键启动**：只需要一个命令即可运行
- ✅ **界面美观**：专业的H5聊天界面
- ✅ **移动端适配**：完美支持手机、平板访问
- ✅ **稳定可靠**：完全修复各种错误，程序稳定
- ✅ **小程序友好**：支持嵌入微信小程序
