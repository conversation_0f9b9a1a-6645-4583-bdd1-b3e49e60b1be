const getApiBaseUrl = () => {
    // 如果是通过file://协议打开（直接打开HTML文件）
    if (window.location.protocol === 'file:') {
        return 'http://localhost:5000';
    }
    // 如果是通过HTTP服务器访问，使用相对路径
    return '';
};

const API_BASE_URL = getApiBaseUrl();

window.DIFY_CONFIG = {
    // HTTP服务器基础URL（自动适配）
    apiUrl: API_BASE_URL + '/api/chat',

    // 流式聊天URL
    streamUrl: API_BASE_URL + '/api/chat/stream',

    // 健康检查URL
    healthUrl: API_BASE_URL + '/api/health',

    // 用户标识前缀
    userPrefix: 'h5-user-',

    // 请求超时时间（毫秒）
    timeout: 300000,

    // 最大重试次数
    maxRetries: 3,

    // 是否启用调试模式
    debug: true
};

// 微信小程序配置
window.WECHAT_CONFIG = {
    // 是否启用微信小程序适配
    enableMiniProgram: true,
    
    // 向小程序发送的消息类型
    messageTypes: {
        WEBVIEW_READY: 'webview-ready',
        MESSAGE_SENT: 'message-sent',
        PAGE_VISIBLE: 'page-visible',
        ERROR_OCCURRED: 'error-occurred'
    }
};

// 应用配置
window.APP_CONFIG = {
    // 应用名称
    appName: 'AI智能助手',
    
    // 最大消息长度
    maxMessageLength: 1000,
    
    // 自动保存对话历史
    autoSaveHistory: true,
    
    // 本地存储键名
    storageKeys: {
        conversationId: 'dify_conversation_id',
        chatHistory: 'chat_history',
        conversationList: 'conversation_list'
    },
    
    // UI配置
    ui: {
        // 消息动画延迟
        messageAnimationDelay: 100,
        
        // 自动滚动延迟
        autoScrollDelay: 100,
        
        // Toast显示时间
        toastDuration: 3000
    }
};
