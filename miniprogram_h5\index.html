<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <!-- 微信小程序webview需要禁用用户缩放以确保良好体验 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>AI助手对话</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 微信JSSDK -->
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <!-- Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- 配置文件 -->
    <script src="config.js"></script>
</head>
<body>
    <div class="container">
        <!-- 上半部分：背景和机器人 -->
        <div class="top-section">
            <div class="background-overlay"></div>
            <!-- 历史记录按钮 -->
            <div class="history-button-container">
                <button type="button" class="history-button" id="historyButton">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" fill="currentColor"/>
                        <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67V7z" fill="currentColor"/>
                    </svg>
                    历史
                </button>
            </div>
            <div class="robot-container">
                <img src="assets/tongtong.gif" alt="通通AI助手" class="robot-avatar" id="robotAvatar">
            </div>
        </div>

        <!-- 下半部分：对话区域 -->
        <div class="bottom-section">
            <!-- 聊天消息区域 -->
            <div class="chat-messages" id="chatMessages">
                <!-- 欢迎消息 -->
                <div class="message bot-message">
                    <div class="message-avatar">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0Qzc5RkYiLz4KPHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSI4IiB5PSI4Ij4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4KPC9zdmc+" alt="AI助手">
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            我是联通数科矛盾调解小助手，我会根据您描述的事件信息，参考相应的法律条文，为您输出调解方案。请给出矛盾的双方的姓名！
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- 输入区域 -->
        <div class="input-container">
            <div class="input-row">
                <!-- 新对话按钮 -->
                <button id="newChatButton" class="new-chat-inline-button" type="button" title="开始新对话">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                        <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="currentColor"/>
                    </svg>
                </button>

                <!-- 输入框区域 -->
                <div class="input-wrapper">
                    <textarea
                        id="messageInput"
                        class="message-input"
                        placeholder="请输入文字"
                        rows="1"
                        maxlength="1000"
                    ></textarea>
                    <button id="sendButton" class="send-button" type="button" title="发送消息" disabled>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div class="loading-overlay hidden" id="loadingOverlay">
            <div class="loading-spinner">
                <div class="spinner"></div>
                <span>AI正在思考中...</span>
            </div>
        </div>

        <!-- 错误提示 -->
        <div class="toast hidden" id="toast">
            <span class="toast-message"></span>
        </div>

        <!-- 历史记录弹窗 -->
        <div class="history-modal hidden" id="historyModal">
            <div class="history-modal-overlay" id="historyModalOverlay"></div>
            <div class="history-modal-content">
                <div class="history-modal-header">
                    <h3>历史对话</h3>
                    <button type="button" class="history-close-btn" id="historyCloseBtn" title="关闭历史记录">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </div>
                <div class="history-modal-body">
                    <div class="history-list" id="historyList">
                        <div class="history-loading">
                            <div class="spinner"></div>
                            <span>加载历史记录中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
