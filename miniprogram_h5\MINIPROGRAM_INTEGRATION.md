# 小程序集成指南

## 用户隔离说明

为了确保不同用户在小程序中看到各自的历史记录，需要在小程序端正确传递用户标识。

## 集成方式

### 1. 在小程序页面中跳转到H5页面时传递用户ID

```javascript
// 小程序页面代码示例
Page({
  data: {
    webviewUrl: ''
  },
  
  onLoad() {
    // 获取用户openid或其他唯一标识
    wx.getUserInfo({
      success: (res) => {
        // 方式1: 使用openid（推荐）
        const openid = wx.getStorageSync('openid') || 'default-user';
        
        // 方式2: 使用unionid
        // const unionid = wx.getStorageSync('unionid') || 'default-user';
        
        // 构建带用户ID的URL
        const baseUrl = 'https://your-domain.com/miniprogram_h5/index.html';
        const webviewUrl = `${baseUrl}?user_id=${openid}`;
        
        this.setData({
          webviewUrl: webviewUrl
        });
      }
    });
  }
});
```

### 2. 小程序WXML模板

```xml
<!-- 小程序页面WXML -->
<web-view src="{{webviewUrl}}"></web-view>
```

### 3. H5页面自动处理用户隔离

H5页面会自动：
1. 从URL参数中提取 `user_id` 或 `openid`
2. 为每个用户创建独立的对话历史
3. 确保用户只能看到自己的历史记录

## 用户ID生成优先级

1. **URL参数** (推荐): `?user_id=xxx` 或 `?openid=xxx`
2. **localStorage**: 浏览器本地存储（仅作为回退方案）

## 安全建议

1. **不要在URL中传递敏感信息**
2. **使用openid而不是真实用户信息**
3. **在生产环境中考虑加密用户ID**

## 测试方法

### 测试用户隔离：
1. 使用不同的user_id参数访问页面：
   - `http://localhost:3000?user_id=user1`
   - `http://localhost:3000?user_id=user2`
2. 在每个页面中发送不同的消息
3. 刷新页面确认各自的历史记录独立保存

### 示例测试URL：
```
http://localhost:3000?user_id=test-user-001
http://localhost:3000?user_id=test-user-002
```
