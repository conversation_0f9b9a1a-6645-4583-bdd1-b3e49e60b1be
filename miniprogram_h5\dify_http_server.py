#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dify Agent HTTP服务器
为H5前端提供API接口
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'dify_client'))

from dify_agent_client import DifyAgentClient
from flask import Flask, request, jsonify, Response, send_from_directory, redirect
from flask_cors import CORS
import json
import uuid
import logging
import threading
import time
import webbrowser

# 配置日志 - 设置为DEBUG级别以查看详细信息
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 配置信息
API_BASE_URL = "http://117.72.181.138:8088/v1"
API_KEY = "app-AaFO7cdbloXPGTYv43M55k4U"

# 创建Dify客户端
dify_client = DifyAgentClient(API_BASE_URL, API_KEY)

# 存储对话状态
conversations = {}

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天请求"""
    try:
        data = request.get_json()
        
        if not data or 'message' not in data:
            return jsonify({'error': '缺少消息内容'}), 400
        
        message = data['message']
        user_id = data.get('user_id', f"user-{uuid.uuid4().hex[:8]}")
        conversation_id = data.get('conversation_id')
        
        logger.info(f"收到聊天请求: user_id={user_id}, message={message[:50]}...")
        
        # 发送消息到Dify
        result = dify_client.send_chat_message(
            query=message,
            user_id=user_id,
            conversation_id=conversation_id,
            response_mode="streaming"
        )
        
        if result:
            # 保存对话状态
            if result.get('conversation_id'):
                conversations[user_id] = result['conversation_id']
                logger.debug(f"保存conversation_id: {result['conversation_id']}")

            response_data = {
                'success': True,
                'answer': result.get('answer', ''),
                'conversation_id': result.get('conversation_id', conversation_id),
                'message_id': result.get('message_id', ''),
                'user_id': user_id
            }

            logger.info(f"返回响应: {response_data['answer'][:50]}...")
            logger.debug(f"完整响应数据: {response_data}")
            return jsonify(response_data)
        else:
            return jsonify({'error': 'Dify API调用失败'}), 500
            
    except Exception as e:
        logger.error(f"聊天处理错误: {e}")
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

@app.route('/api/chat/stream', methods=['POST'])
def chat_stream():
    """处理流式聊天请求"""
    try:
        data = request.get_json()
        
        if not data or 'message' not in data:
            return jsonify({'error': '缺少消息内容'}), 400
        
        message = data['message']
        user_id = data.get('user_id', f"user-{uuid.uuid4().hex[:8]}")
        conversation_id = data.get('conversation_id')
        
        logger.info(f"收到流式聊天请求: user_id={user_id}, message={message[:50]}...")
        
        def generate():
            try:
                # 发送消息到Dify
                result = dify_client.send_chat_message(
                    query=message,
                    user_id=user_id,
                    conversation_id=conversation_id,
                    response_mode="streaming"
                )
                
                if result:
                    # 保存对话状态
                    if result.get('conversation_id'):
                        conversations[user_id] = result['conversation_id']
                    
                    # 返回完整响应
                    response_data = {
                        'success': True,
                        'answer': result.get('answer', ''),
                        'conversation_id': result.get('conversation_id', conversation_id),
                        'message_id': result.get('message_id', ''),
                        'user_id': user_id,
                        'done': True
                    }
                    
                    yield f"data: {json.dumps(response_data, ensure_ascii=False)}\n\n"
                else:
                    error_data = {'error': 'Dify API调用失败', 'done': True}
                    yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
                    
            except Exception as e:
                logger.error(f"流式聊天处理错误: {e}")
                error_data = {'error': f'服务器错误: {str(e)}', 'done': True}
                yield f"data: {json.dumps(error_data, ensure_ascii=False)}\n\n"
        
        return Response(generate(), mimetype='text/plain')
        
    except Exception as e:
        logger.error(f"流式聊天请求错误: {e}")
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500

@app.route('/api/conversations/<user_id>', methods=['GET'])
def get_conversations(user_id):
    """获取用户的会话列表"""
    try:
        logger.info(f"=== 获取用户会话列表 API 被调用 ===")
        logger.info(f"user_id={user_id}")
        print(f"DEBUG: 获取用户会话列表: user_id={user_id}")

        # 调用Dify API获取会话列表
        logger.info("正在调用 dify_client.get_conversations...")
        result = dify_client.get_conversations(user_id=user_id, limit=5)
        logger.info(f"Dify API 返回结果: {result}")

        if result:
            return jsonify({
                'success': True,
                'conversations': result.get('data', []),
                'user_id': user_id
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取会话列表失败',
                'conversations': []
            })

    except Exception as e:
        logger.error(f"获取会话列表错误: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}',
            'conversations': []
        }), 500

@app.route('/api/conversations/<conversation_id>/messages', methods=['GET'])
def get_conversation_messages(conversation_id):
    """获取指定会话的消息历史"""
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({'error': '缺少user_id参数'}), 400

        logger.info(f"获取会话消息: conversation_id={conversation_id}, user_id={user_id}")

        # 调用Dify API获取会话消息
        result = dify_client.get_conversation_messages(conversation_id=conversation_id, user_id=user_id)

        if result:
            return jsonify({
                'success': True,
                'messages': result.get('data', []),
                'conversation_id': conversation_id
            })
        else:
            return jsonify({
                'success': False,
                'error': '获取会话消息失败',
                'messages': []
            })

    except Exception as e:
        logger.error(f"获取会话消息错误: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}',
            'messages': []
        }), 500

@app.route('/api/health', methods=['GET'])
def health():
    """健康检查接口"""
    return jsonify({
        'status': 'ok',
        'service': 'Dify Agent HTTP Server',
        'timestamp': time.time()
    })

@app.route('/api/conversation/<user_id>', methods=['GET'])
def get_conversation(user_id):
    """获取用户的对话ID"""
    conversation_id = conversations.get(user_id)
    return jsonify({
        'user_id': user_id,
        'conversation_id': conversation_id
    })



@app.route('/api/info')
def api_info():
    """API信息接口"""
    return jsonify({
        'service': 'Dify Agent HTTP Server with Static Files',
        'version': '1.0.0',
        'endpoints': {
            'chat': '/api/chat',
            'chat_stream': '/api/chat/stream',
            'health': '/api/health',
            'conversations': '/api/conversations/<user_id>',
            'info': '/api/info'
        },
        'static_files': 'Serving from current directory'
    })

# 静态文件路由（必须放在最后，避免拦截API路由）
@app.route('/')
def index():
    """根路径，重定向到H5页面"""
    return redirect('/index.html')

@app.route('/<path:filename>')
def static_files(filename):
    """提供静态文件服务"""
    try:
        return send_from_directory('.', filename)
    except FileNotFoundError:
        return jsonify({'error': 'File not found'}), 404

def main():
    """主函数"""
    print("=== Dify Agent HTTP服务器 ===")
    print(f"API服务器: {API_BASE_URL}")
    print(f"API密钥: {API_KEY[:20]}...")
    print()
    
    # 测试Dify连接
    try:
        print("正在测试Dify API连接...")
        test_result = dify_client.send_chat_message(
            query="你好",
            user_id="test-user",
            response_mode="streaming"
        )
        if test_result:
            print("✅ Dify API连接测试成功")
        else:
            print("❌ Dify API连接测试失败")
            return
    except Exception as e:
        print(f"❌ Dify API连接测试失败: {e}")
        return
    
    print()

    print("   - POST /api/chat - 普通聊天")
    print("   - POST /api/chat/stream - 流式聊天")
    print("   - GET /api/conversations/<user_id> - 获取会话列表")
    print("   - GET /api/conversations/<conversation_id>/messages - 获取会话消息")
    print("   - GET /api/conversation/<user_id> - 获取对话ID")
    print("   - GET /api/health - 健康检查")
    print("   - 直接访问 http://localhost:5000 开始对话")
    print("   - 使用Ctrl+C停止服务器")
    print()
 
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n服务器已停止")

if __name__ == "__main__":
    main()
